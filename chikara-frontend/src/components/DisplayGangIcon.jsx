import { cn } from "@/lib/utils";
import defaultGangIcon from "@/assets/icons/navitems/gang.png";

const CDN_URL = import.meta.env.VITE_IMAGE_CDN_URL || import.meta.env.VITE_API_BASE_URL;
const DEFAULT_GANG_ICON = defaultGangIcon || `https://cloudflare-image.jamessut.workers.dev/ui-images/OMun8OF.png`;

const gangURL = (gang) => {
    if (gang?.avatar) {
        if (gang?.avatar.startsWith("http")) {
            return gang.avatar;
        }
        return `${CDN_URL}/${gang.avatar}`;
    } else {
        return DEFAULT_GANG_ICON;
    }
};

export const DisplayGangIcon = ({ src, className, onAnimationEnd, loading }) => {
    return (
        <img
            src={gangURL(src)}
            className={cn(className ? className : "size-full rounded-md object-cover")}
            alt={`Gang Avatar`}
            loading={loading}
            onAnimationEnd={() => onAnimationEnd()}
            onError={(e) => {
                e.target.onError = null;
                e.target.src = DEFAULT_GANG_ICON;
            }}
        />
    );
};
