import { sceneManager } from "@/helpers/sceneManager";

const CDN_URL = import.meta.env.VITE_IMAGE_CDN_URL || import.meta.env.VITE_API_BASE_URL;

function ProfileBanner({ userData, isYourProfile }) {
    const getProfileBanner = () => {
        if (isYourProfile) {
            if (userData.profileBanner) {
                if (userData.profileBanner.startsWith("http")) {
                    return userData.profileBanner;
                }
                return `${CDN_URL}/${userData.profileBanner}`;
            } else {
                return sceneManager("street1");
            }
        }

        if (userData && userData.profileBanner) {
            if (userData.profileBanner.startsWith("http")) {
                return userData.profileBanner;
            }
            return `${CDN_URL}/${userData.profileBanner}`;
        } else {
            return sceneManager("street1");
        }
    };

    return (
        <div className="relative h-48 max-w-3xl mx-auto lg:max-w-7xl overflow-hidden md:mt-6 md:rounded-t-lg lg:h-72">
            <div
                className="-translate-x-1/2 absolute inset-0 left-1/2 flex w-full justify-center overflow-hidden"
                aria-hidden="true"
            >
                <div className="grow bg-sky-900/75" />
                <img src={getProfileBanner()} alt="" className="aspect-4/3 w-full object-cover object-center" />
                <div className="grow bg-sky-900/75" />
            </div>
            <header className="headerGradient relative py-10">
                <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 "></div>
            </header>
        </div>
    );
}

export default ProfileBanner;
