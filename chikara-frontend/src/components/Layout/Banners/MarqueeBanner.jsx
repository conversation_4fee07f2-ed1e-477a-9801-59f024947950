import Marquee from "@/components/ui/marquee";
import useGameConfig from "@/hooks/useGameConfig";
import { useNormalStore } from "../../../app/store/stores";

function MarqueeBanner() {
    const { marqueeText, setMarqueeText } = useNormalStore();
    const { MARQUEE_BANNER_DISABLED } = useGameConfig();

    const handleClearMarquee = () => {
        setMarqueeText(null);
    };
    if (MARQUEE_BANNER_DISABLED) return null;

    return (
        <>
            {marqueeText && (
                <Marquee
                    className="pointer-events-none h-8 w-full font-bold bg-linear-to-b from-black/85 to-black/50 md:h-16 text-gray-200"
                    onFinish={() => handleClearMarquee()}
                >
                    {marqueeText}
                </Marquee>
            )}
        </>
    );
}

export default MarqueeBanner;
